// TypeScript interfaces for tariff management

export interface HourlyRate {
  id?: string;
  hourFrom: number;
  hourTo?: number;
  pricePerKwh: number; // Euro
  sessionFee: number; // Euro
  priceType: "EPEX" | "FIXED";
  // EPEX-specific fields
  fallbackPricePerKwh?: number; // Euro
  minPricePerKwh?: number; // Cent (converted from euro in frontend)
  maxPricePerKwh?: number; // Cent (converted from euro in frontend)
  marginType?: "fixed" | "percentage";
  marginValue?: number; // Euro
  taxesAndLevies?: number; // Cent (converted from euro in frontend)
  epexAveragingPeriodHours?: number;
}

export interface BlockingFeeSchedule {
  perMinute: number;
  maxFee: number;
  gracePeriod: number;
}

export interface PriceFormData {
  pricePerKwh: string;
  sessionFee: string;
  priceType: "EPEX" | "FIXED";
  // EPEX-specific fields
  fallbackPricePerKwh: string;
  minPricePerKwh: string;
  maxPricePerKwh: string;
  marginType: "fixed" | "percentage";
  marginValue: string;
  taxesAndLevies: string;
  epexAveragingPeriodHours: string;
  // Validity dates (not used anymore, will come from main tariff form)
  validFrom?: string;
  validTo?: string;
}

export interface PriceFormProps {
  priceForm: PriceFormData;
  setPriceForm: (form: PriceFormData) => void;
  type: "AC" | "DC";
  showEpexSimulator: boolean;
  setShowEpexSimulator: (show: boolean) => void;
}

export interface HourCellProps {
  hour: number;
  day: number;
  isSelected: boolean;
  hasPrice?: boolean;
  priceInfo?: { pricePerKwh: number; sessionFee: number };
  onToggle: (hour: number, day: number, shiftKey?: boolean) => void;
  compact?: boolean;
}

export interface PriceOverviewProps {
  type: "AC" | "DC";
  currentPricingType: "AC" | "DC" | null;
  acHourlyRates: { [key: string]: HourlyRate };
  dcHourlyRates: { [key: string]: HourlyRate };
  onRemovePrices: (keys: string[]) => void;
}

export interface BlockingFeeFormProps {
  type: "AC" | "DC";
  onApplyBlockingFee: (perMinute: number, maxFee: number, gracePeriod: number) => void;
  onRemoveBlockingFees: (keys: string[]) => void;
  currentPricingType: "AC" | "DC" | null;
  acBlockingFeeSchedules: { [key: string]: BlockingFeeSchedule };
  dcBlockingFeeSchedules: { [key: string]: BlockingFeeSchedule };
  showBlockingFeeSimulator: boolean;
  setShowBlockingFeeSimulator: (show: boolean) => void;
}

export interface HelpTextProps {
  children: React.ReactNode;
  className?: string;
}

export interface PriceGroup {
  startDay: number;
  startHour: number;
  endDay: number;
  endHour: number;
  startWeekday: string;
  endWeekday: string;
  pricePerKwh: number;
  sessionFee: number;
  priceType: string;
  count: number;
  // EPEX-specific fields
  fallbackPricePerKwh?: number;
  minPricePerKwh?: number;
  maxPricePerKwh?: number;
  marginType?: string;
  marginValue?: number;
  taxesAndLevies?: number;
  epexAveragingPeriodHours?: number;
}

export interface BlockingFeeGroup {
  startDay: number;
  startHour: number;
  endDay: number;
  endHour: number;
  startWeekday: string;
  endWeekday: string;
  perMinute: number;
  maxFee: number;
  gracePeriod: number;
  count: number;
}

export interface CellSelection {
  [key: string]: boolean;
}

export interface LastSelectedCell {
  day: number;
  hour: number;
}

// Validation Error Interface
export interface ValidationError {
  field: string;
  message: string;
  type: "AC" | "DC" | "general";
}

// API Data Structures for Strapi 5
export interface TariffCreateData {
  name: string;
  mandants?: string[]; // Array of mandant documentIds
  priceAC?: PriceComponentData[];
  blockFeeAC?: BlockFeeComponentData[];
  priceDC?: PriceComponentData[];
  blockFeeDC?: BlockFeeComponentData[];
}

export interface PriceComponentData {
  validFrom: string; // ISO datetime
  validTo: string | null; // ISO datetime or null for unlimited validity
  dailySchedules: DailyScheduleData[];
  changeDate: string; // ISO datetime
  users_permissions_user: string; // User documentId who created/modified this price
}

export interface DailyScheduleData {
  dayOfWeek: "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";
  hourlyRate: HourlyRateData[];
}

export interface HourlyRateData {
  hourFrom: number;
  hourTo?: number;
  // Decimal fields (Euro values)
  pricePerKwh: number;
  sessionFee: number;
  fallbackPricePerKwh?: number;
  marginValue?: number;
  // Integer fields (Cent values)
  minPricePerKwh?: number; // Cent
  maxPricePerKwh?: number; // Cent
  taxesAndLevies?: number; // Cent (BigInteger in Strapi)
  // Enum and other fields
  priceType: "EPEX" | "FIXED";
  marginType?: "fixed" | "percentage";
  epexAveragingPeriodHours?: number;
}

export interface BlockFeeComponentData {
  validFrom: string; // ISO datetime
  validTo: string | null; // ISO datetime or null for unlimited validity
  blockFeeSchedule: BlockFeeScheduleData[];
  changeDate: string; // ISO datetime
  users_permissions_user: string; // User documentId who created/modified this block fee
}

export interface BlockFeeScheduleData {
  dayOfWeek: "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";
  startHour: number;
  endHour: number;
  perMinute: number;
  maxFee: number;
  gracePeriod: number;
}
