"use client";

import * as React from "react";
import { useState, useMemo, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "~/components/AuthContext";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "~/components/ui/tabs";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { tariffsApiStrapi } from "~/services/tariffs-api-strapi";
import { EPEXPriceSimulator } from "../components/EPEXPriceSimulator";
import { BlockingFeeSimulator } from "../components/BlockingFeeSimulator";

// Import new components and utilities
import { PriceForm } from "../components/PriceForm";
import { PriceOverview } from "../components/PriceOverview";
import { BlockingFeeForm } from "../components/BlockingFeeForm";
import { HelpText } from "../components/HelpText";
import { HourlyScheduleGrid } from "../components/HourlyScheduleGrid";
import { useCalendarSelection } from "../hooks/useCalendarSelection";
import { convertFormDataToTariffData } from "../utils/dataMapping";
import { validateTariffData } from "../utils/validation";
import { useMandant } from "~/components/MandantContext";
import type {
  HourlyRate,
  PriceFormData,
  BlockingFeeSchedule,
  ValidationError
} from "../types/tariff.types";



export default function TarifNeuPage() {
  // Basic state
  const { user, token, loading } = useAuth();
  const { activeMandant } = useMandant();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  // URL-Parameter für erweiterte Preise
  const tariffId = searchParams.get('tariffId');
  const priceType = searchParams.get('type') as 'AC' | 'DC' | null;
  const mode = searchParams.get('mode'); // 'price' oder 'blockfee'
  const urlValidFrom = searchParams.get('validFrom');
  const urlValidTo = searchParams.get('validTo');

  // Prüfen ob wir im "Preis hinzufügen" Modus sind
  const isAddingPriceMode = !!(tariffId && priceType && mode);
  const [existingTariffName, setExistingTariffName] = useState<string>("");

  // Create default validity dates
  const defaultValidFrom = useMemo(() => {
    if (isAddingPriceMode && urlValidFrom) {
      return urlValidFrom;
    }

    if (isAddingPriceMode) {
      // Default: 1 Stunde in der Zukunft, auf die nächste volle Stunde aufgerundet
      const now = new Date();
      const oneHourFromNow = new Date(now);

      // Erst +1 Stunde addieren
      oneHourFromNow.setHours(oneHourFromNow.getHours() + 1);

      // Dann prüfen ob wir bereits auf einer vollen Stunde sind
      // Wenn nicht, auf die nächste volle Stunde aufrunden
      if (oneHourFromNow.getMinutes() > 0 || oneHourFromNow.getSeconds() > 0 || oneHourFromNow.getMilliseconds() > 0) {
        oneHourFromNow.setHours(oneHourFromNow.getHours() + 1);
      }

      // Minuten, Sekunden und Millisekunden auf 0 setzen
      oneHourFromNow.setMinutes(0);
      oneHourFromNow.setSeconds(0);
      oneHourFromNow.setMilliseconds(0);

      return oneHourFromNow.toISOString().slice(0, 16);
    } else {
      // Normaler Modus: aktuelles Datum/Zeit
      return new Date().toISOString().slice(0, 16);
    }
  }, [isAddingPriceMode, urlValidFrom]);

  const defaultValidTo = useMemo(() => {
    console.log(urlValidTo, "<< urlvalidto")
    if (isAddingPriceMode && urlValidTo) {
      return urlValidTo;
    }
    // Im Preis-hinzufügen Modus: standardmäßig leer (Button wird angezeigt)
    return "";
  }, [isAddingPriceMode, urlValidTo]);

  // New tariff form state with validity dates
  const [newTariff, setNewTariff] = useState({
    name: "",
    validFrom: defaultValidFrom,
    validTo: defaultValidTo,
  });

  // State für das optionale "Gültig bis" Feld
  const [showValidUntil, setShowValidUntil] = useState(() => {
    // Im Preis-hinzufügen Modus: standardmäßig Button anzeigen (false)
    // Im normalen Modus: nur anzeigen wenn defaultValidTo gesetzt ist
    if (isAddingPriceMode) {
      return !!urlValidTo; // Nur anzeigen wenn explizit in URL gesetzt
    }
    return !!defaultValidTo;
  });

  // AC/DC pricing state
  const [hasACPricing, setHasACPricing] = useState(false);
  const [hasDCPricing, setHasDCPricing] = useState(false);

  // Tab state - now using AC/DC as main tabs
  const [activeTab, setActiveTab] = useState<string>("ac");
  const [activeSubTab, setActiveSubTab] = useState<string>("pricing");

  // Separate price form states for AC and DC (without validity dates)
  const [acPriceForm, setACPriceForm] = useState<PriceFormData>({
    pricePerKwh: "0.00",
    sessionFee: "0.00",
    priceType: "FIXED",
    // EPEX-specific fields
    fallbackPricePerKwh: "0.00",
    minPricePerKwh: "0.00",
    maxPricePerKwh: "1.00",
    marginType: "fixed",
    marginValue: "0.05",
    taxesAndLevies: "0.15",
    epexAveragingPeriodHours: "1",
    // Validity dates will come from newTariff
    validFrom: "",
    validTo: "",
  });

  const [dcPriceForm, setDCPriceForm] = useState<PriceFormData>({
    pricePerKwh: "0.00",
    sessionFee: "0.00",
    priceType: "FIXED",
    // EPEX-specific fields
    fallbackPricePerKwh: "0.00",
    minPricePerKwh: "0.00",
    maxPricePerKwh: "1.00",
    marginType: "fixed",
    marginValue: "0.05",
    taxesAndLevies: "0.15",
    epexAveragingPeriodHours: "1",
    // Validity dates will come from newTariff
    validFrom: "",
    validTo: "",
  });

  // Current pricing type being edited
  const [currentPricingType, setCurrentPricingType] = useState<"AC" | "DC" | null>(null);

  // Pricing data for AC and DC
  const [acHourlyRates, setACHourlyRates] = useState<{
    [key: string]: HourlyRate;
  }>({});

  const [dcHourlyRates, setDCHourlyRates] = useState<{
    [key: string]: HourlyRate;
  }>({});

  // Blocking fee state
  const [acBlockingFeeSchedules, setACBlockingFeeSchedules] = useState<{
    [key: string]: BlockingFeeSchedule;
  }>({});
  const [dcBlockingFeeSchedules, setDCBlockingFeeSchedules] = useState<{
    [key: string]: BlockingFeeSchedule;
  }>({});

  // State für die Sichtbarkeit des EPEX-Simulators
  const [showEpexSimulator, setShowEpexSimulator] = useState(false);

  // State für die Sichtbarkeit des Blockiergebühren-Simulators
  const [showBlockingFeeSimulator, setShowBlockingFeeSimulator] = useState(false);

  // Use calendar selection hook
  const {
    selectedHours,
    selectedBlockingHours,
    setSelectedHours,
    setSelectedBlockingHours,
    toggleHourSelection,
    toggleAllHoursForDay,
    toggleHourForAllDays,
    toggleBlockingHourSelection,
    toggleAllHoursForDayBlocking,
    toggleHourForAllDaysBlocking,
    clearSelections,
  } = useCalendarSelection();

  // Effect zum Laden der Tarif-Daten wenn im "Preis hinzufügen" Modus
  useEffect(() => {
    const loadExistingTariff = async () => {
      if (!isAddingPriceMode || !tariffId) return;

      try {
        // Lade den bestehenden Tarif, um den Namen zu bekommen
        const response = await tariffsApiStrapi.getByDocumentId(tariffId);

        console.log('Tariff API response:', response); // Debug log

        if (response && response.data) {
          // In Strapi 5 liegen die Daten direkt auf response.data
          const tariffName = response.data.name || `Tarif (${tariffId})`;
          setExistingTariffName(tariffName);
          console.log('Set tariff name to:', tariffName); // Debug log
        } else {
          console.warn('No tariff data found in response:', response);
          setExistingTariffName(`Tarif (${tariffId})`);
        }

        // Setze die entsprechenden Pricing-Flags basierend auf dem Typ
        if (priceType === 'AC') {
          setHasACPricing(true);
          setCurrentPricingType('AC');
          setActiveTab('ac');
        } else if (priceType === 'DC') {
          setHasDCPricing(true);
          setCurrentPricingType('DC');
          setActiveTab('dc');
        }

        // Setze den entsprechenden Sub-Tab basierend auf dem Modus
        if (mode === 'price') {
          setActiveSubTab('pricing');
        } else if (mode === 'blockfee') {
          setActiveSubTab('blocking');
        }
      } catch (error) {
        console.error('Error loading existing tariff:', error);
        setError('Fehler beim Laden der Tarif-Daten');
        // Fallback auf DocumentId wenn das Laden fehlschlägt
        setExistingTariffName(`Tarif (${tariffId})`);
      }
    };

    loadExistingTariff();
  }, [isAddingPriceMode, tariffId, priceType, mode]);

  // Effect zum Aktualisieren der Gültigkeitsdaten wenn sich die Defaults ändern
  useEffect(() => {
    setNewTariff(prev => ({
      ...prev,
      validFrom: defaultValidFrom,
      validTo: defaultValidTo
    }));
  }, [defaultValidFrom, defaultValidTo]);

  // Save tariff function
  const handleSaveTariff = async () => {
    if (!activeMandant) {
      setError("Kein Mandant ausgewählt. Bitte wählen Sie einen Mandanten aus.");
      return;
    }

    if (!user?.documentId) {
      setError("Benutzer-Informationen nicht verfügbar. Bitte melden Sie sich erneut an.");
      return;
    }

    // Clear previous errors
    setError("");
    setValidationErrors([]);
    setSuccessMessage(null);

    // Validate form data - use central validity dates for both AC and DC
    const errors = validateTariffData(
      newTariff.name,
      hasACPricing,
      hasDCPricing,
      acHourlyRates,
      dcHourlyRates,
      newTariff.validFrom,
      newTariff.validTo || "", // Use empty string if no validTo is set
      newTariff.validFrom,
      newTariff.validTo || ""
    );

    if (errors.length > 0) {
      setValidationErrors(errors);
      setError("Bitte korrigieren Sie die Validierungsfehler vor dem Speichern.");
      return;
    }

    setIsLoading(true);

    try {
      // Convert form data to Strapi format - use central validity dates
      const tariffData = convertFormDataToTariffData(
        newTariff.name,
        activeMandant.documentId,
        user.documentId, // Add user documentId for tracking who created/modified the tariff
        hasACPricing,
        hasDCPricing,
        acHourlyRates,
        dcHourlyRates,
        acBlockingFeeSchedules,
        dcBlockingFeeSchedules,
        newTariff.validFrom,
        newTariff.validTo || null, // Use null if validTo is empty (unlimited validity)
        newTariff.validFrom,
        newTariff.validTo || null  // Use null if validTo is empty (unlimited validity)
      );

      console.log("Sending tariff data to API:", JSON.stringify(tariffData, null, 2));

      // Save to Strapi
      const response = await tariffsApiStrapi.create(tariffData);

      console.log("Tariff created successfully:", response);

      setSuccessMessage(`Tarif "${newTariff.name}" wurde erfolgreich erstellt!`);

      // Reset form after successful save
      setTimeout(() => {
        router.push("/tariff");
      }, 2000);

    } catch (err: any) {
      console.error("Error saving tariff:", err);
      setError(`Fehler beim Speichern des Tarifs: ${err.message || "Unbekannter Fehler"}`);
    } finally {
      setIsLoading(false);
    }
  };



  // Funktion zum Anwenden der Blockiergebühren auf ausgewählte Stunden
  const applyBlockingFeeToSelectedHours = (perMinute: number, maxFee: number, gracePeriod: number) => {
    try {
      if (!currentPricingType) return;

      const currentSchedules = currentPricingType === "AC" ? acBlockingFeeSchedules : dcBlockingFeeSchedules;
      const setCurrentSchedules = currentPricingType === "AC" ? setACBlockingFeeSchedules : setDCBlockingFeeSchedules;

      // Erstelle ein neues Objekt mit den Blockiergebühren
      const newBlockingFeeSchedules = { ...currentSchedules };

      // Füge für jede ausgewählte Stunde die Blockiergebühr hinzu
      Object.entries(selectedBlockingHours).forEach(([key, isSelected]) => {
        if (isSelected) {
          newBlockingFeeSchedules[key] = {
            perMinute,
            maxFee,
            gracePeriod,
          };
        }
      });

      // Aktualisiere die Blockiergebühren
      setCurrentSchedules(newBlockingFeeSchedules);

      // Zurücksetzen der Auswahl
      clearSelections();
    } catch (error) {
      console.error("Fehler beim Anwenden der Blockiergebühr:", error);
    }
  };

  // Funktion zum Entfernen von Blockiergebühren
  const removeBlockingFees = (keys: string[]) => {
    try {
      if (!currentPricingType) return;

      const currentSchedules = currentPricingType === "AC" ? acBlockingFeeSchedules : dcBlockingFeeSchedules;
      const setCurrentSchedules = currentPricingType === "AC" ? setACBlockingFeeSchedules : setDCBlockingFeeSchedules;

      // Erstelle ein neues Objekt ohne die zu entfernenden Keys
      const newBlockingFeeSchedules = { ...currentSchedules };
      keys.forEach(key => {
        delete newBlockingFeeSchedules[key];
      });

      // Aktualisiere die Blockiergebühren
      setCurrentSchedules(newBlockingFeeSchedules);
    } catch (error) {
      console.error("Fehler beim Entfernen der Blockiergebühren:", error);
    }
  };

  // Funktion zum Entfernen von Preisen
  const removePrices = (keys: string[]) => {
    try {
      if (!currentPricingType) return;

      const currentRates = currentPricingType === "AC" ? acHourlyRates : dcHourlyRates;
      const setCurrentRates = currentPricingType === "AC" ? setACHourlyRates : setDCHourlyRates;

      // Erstelle ein neues Objekt ohne die zu entfernenden Keys
      const newHourlyRates = { ...currentRates };
      keys.forEach(key => {
        delete newHourlyRates[key];
      });

      // Aktualisiere die Preise
      setCurrentRates(newHourlyRates);
    } catch (error) {
      console.error("Fehler beim Entfernen der Preise:", error);
    }
  };

  // Funktion zum kompletten Entfernen eines Preistyps (AC oder DC)
  const removePricingType = (type: "AC" | "DC") => {
    try {
      if (type === "AC") {
        // Entferne alle AC-Preise und Blockiergebühren
        setACHourlyRates({});
        setACBlockingFeeSchedules({});
        setHasACPricing(false);

        // Wechsle zu DC wenn verfügbar, sonst deaktiviere
        if (hasDCPricing) {
          setCurrentPricingType("DC");
          setActiveTab("dc");
        } else {
          setCurrentPricingType(null);
          setActiveTab("");
        }
      } else if (type === "DC") {
        // Entferne alle DC-Preise und Blockiergebühren
        setDCHourlyRates({});
        setDCBlockingFeeSchedules({});
        setHasDCPricing(false);

        // Wechsle zu AC wenn verfügbar, sonst deaktiviere
        if (hasACPricing) {
          setCurrentPricingType("AC");
          setActiveTab("ac");
        } else {
          setCurrentPricingType(null);
          setActiveTab("");
        }
      }

      // Zurücksetzen der Auswahlen
      clearSelections();
    } catch (error) {
      console.error("Fehler beim Entfernen des Preistyps:", error);
    }
  };

  // Funktion zum Anwenden des Preises auf ausgewählte Stunden
  const applyPriceToSelectedHours = () => {
    try {
      if (!currentPricingType) return;

      const currentRates = currentPricingType === "AC" ? acHourlyRates : dcHourlyRates;
      const setCurrentRates = currentPricingType === "AC" ? setACHourlyRates : setDCHourlyRates;
      const currentPriceForm = currentPricingType === "AC" ? acPriceForm : dcPriceForm;

      // Erstelle ein neues Objekt mit den Preisen
      const newHourlyRates = { ...currentRates };

      // Füge für jede ausgewählte Stunde den Preis hinzu
      Object.entries(selectedHours).forEach(([key, isSelected]) => {
        if (isSelected) {
          const hour = parseInt(key.split("-")[1] || "0");
          const hourlyRate: HourlyRate = {
            hourFrom: hour,
            hourTo: hour === 23 ? 23 : hour + 1, // Handle hour 23 edge case (max 23)
            pricePerKwh: parseFloat(currentPriceForm.pricePerKwh) || 0,
            sessionFee: parseFloat(currentPriceForm.sessionFee) || 0,
            priceType: currentPriceForm.priceType,
          };

          // Add EPEX-specific fields if EPEX is selected
          if (currentPriceForm.priceType === "EPEX") {
            hourlyRate.fallbackPricePerKwh = parseFloat(currentPriceForm.fallbackPricePerKwh) || 0;
            // Convert euro values to cents for integer fields
            hourlyRate.minPricePerKwh = Math.round((parseFloat(currentPriceForm.minPricePerKwh) || 0) * 100);
            hourlyRate.maxPricePerKwh = Math.round((parseFloat(currentPriceForm.maxPricePerKwh) || 0) * 100);
            hourlyRate.marginType = currentPriceForm.marginType;
            hourlyRate.marginValue = parseFloat(currentPriceForm.marginValue) || 0;
            // Convert euro value to cents for biginteger field
            hourlyRate.taxesAndLevies = Math.round((parseFloat(currentPriceForm.taxesAndLevies) || 0) * 100);
            hourlyRate.epexAveragingPeriodHours = parseInt(currentPriceForm.epexAveragingPeriodHours) || 1;
          }

          newHourlyRates[key] = hourlyRate;
        }
      });

      // Aktualisiere die Preise
      setCurrentRates(newHourlyRates);

      // Zurücksetzen der Auswahl
      clearSelections();
    } catch (error) {
      console.error("Fehler beim Anwenden des Preises:", error);
    }
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-bold">
            {isAddingPriceMode
              ? `Neue ${priceType}-${mode === 'price' ? 'Preise' : 'Blockiergebühren'} erstellen`
              : 'Neuen Tarif erstellen'
            }
          </h1>
          {isAddingPriceMode && (
            <p className="text-gray-600 mt-1">
              Für Tarif: {existingTariffName || `Tarif (${tariffId})`}
            </p>
          )}
        </div>
        <button
          onClick={() => {
            if (isAddingPriceMode) {
              router.push(`/tariff/${tariffId}`);
            } else {
              router.push("/tariff");
            }
          }}
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
        >
          Zurück
        </button>
      </div>

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {successMessage}
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {validationErrors.length > 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <h4 className="font-bold mb-2">Validierungsfehler:</h4>
          <ul className="list-disc list-inside space-y-1">
            {validationErrors.map((error, index) => (
              <li key={index}>
                <span className="font-medium">{error.type}:</span> {error.message}
              </li>
            ))}
          </ul>
        </div>
      )}

      <form className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Allgemeine Informationen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
              <div className="flex flex-col h-full">
                {isAddingPriceMode ? (
                  <>
                    <Label>
                      Tarif
                      <HelpText>
                        Sie fügen neue {priceType}-{mode === 'price' ? 'Preise' : 'Blockiergebühren'} zu diesem bestehenden Tarif hinzu.
                      </HelpText>
                    </Label>
                    <div className="mt-auto p-3 bg-gray-50 border rounded-md">
                      <div className="font-medium text-gray-900">
                        {existingTariffName || `Tarif (${tariffId})`}
                      </div>
                      <div className="text-sm text-gray-600">
                        {priceType}-{mode === 'price' ? 'Preise' : 'Blockiergebühren'} hinzufügen
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <Label htmlFor="tariff-name">
                      Name
                      <HelpText>
                        Eindeutiger Name für den Tarif. Wird in der Übersicht und bei der Zuordnung zu Terminals angezeigt.
                      </HelpText>
                    </Label>
                    <Input
                      id="tariff-name"
                      type="text"
                      value={newTariff.name}
                      onChange={(e) =>
                        setNewTariff({ ...newTariff, name: e.target.value })
                      }
                      required
                      className="mt-auto"
                    />
                  </>
                )}
              </div>
              <div className="flex flex-col h-full">
                <Label htmlFor="tariff-valid-from">
                  Gültig von
                  <HelpText>
                    {isAddingPriceMode
                      ? "Datum und Uhrzeit, ab wann diese neuen Preise gültig sind. Muss mindestens 1 Stunde in der Zukunft liegen."
                      : "Datum und Uhrzeit, ab wann dieser Tarif gültig ist. Gilt für alle AC/DC-Preise und Blockiergebühren."
                    }
                  </HelpText>
                </Label>
                <Input
                  id="tariff-valid-from"
                  type="datetime-local"
                  value={newTariff.validFrom}
                  onChange={(e) => {
                    const selectedDate = new Date(e.target.value);

                    if (isAddingPriceMode) {
                      // Mindestzeit: nächste volle Stunde (nach +1h und aufrunden)
                      const now = new Date();
                      const nextHour = new Date(now);

                      // Erst +1 Stunde addieren
                      nextHour.setHours(nextHour.getHours() + 1);

                      // Dann prüfen ob wir bereits auf einer vollen Stunde sind
                      // Wenn nicht, auf die nächste volle Stunde aufrunden
                      if (nextHour.getMinutes() > 0 || nextHour.getSeconds() > 0 || nextHour.getMilliseconds() > 0) {
                        nextHour.setHours(nextHour.getHours() + 1);
                      }

                      // Minuten, Sekunden und Millisekunden auf 0 setzen
                      nextHour.setMinutes(0);
                      nextHour.setSeconds(0);
                      nextHour.setMilliseconds(0);

                      if (selectedDate < nextHour) {
                        setError("Das Datum muss mindestens zur nächsten vollen Stunde in der Zukunft liegen.");
                        return;
                      } else {
                        setError("");
                      }
                    } else {
                      setError("");
                    }

                    setNewTariff({ ...newTariff, validFrom: e.target.value });
                  }}
                  required
                  className="mt-auto"
                  min={isAddingPriceMode ? (() => {
                    const now = new Date();
                    const nextHour = new Date(now);

                    // Erst +1 Stunde addieren
                    nextHour.setHours(nextHour.getHours() + 1);

                    // Dann prüfen ob wir bereits auf einer vollen Stunde sind
                    // Wenn nicht, auf die nächste volle Stunde aufrunden
                    if (nextHour.getMinutes() > 0 || nextHour.getSeconds() > 0 || nextHour.getMilliseconds() > 0) {
                      nextHour.setHours(nextHour.getHours() + 1);
                    }

                    // Minuten, Sekunden und Millisekunden auf 0 setzen
                    nextHour.setMinutes(0);
                    nextHour.setSeconds(0);
                    nextHour.setMilliseconds(0);

                    return nextHour.toISOString().slice(0, 16);
                  })() : undefined}
                />
              </div>
              <div className="flex flex-col h-full">
                {!showValidUntil ? (
                  <>
                    <Label className="mb-2">
                      Gültigkeitsdauer
                      <HelpText>
                        {isAddingPriceMode
                          ? "Die neuen Preise sind standardmäßig unbegrenzt gültig. Sie können optional ein Enddatum festlegen."
                          : "Der Tarif ist standardmäßig unbegrenzt gültig. Sie können optional ein Enddatum festlegen."
                        }
                      </HelpText>
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowValidUntil(true)}
                      className="flex items-center gap-2 w-fit mt-auto"
                    >
                      <span>📅</span>
                      Gültig bis hinzufügen
                    </Button>
                  </>
                ) : (
                  <>
                    <div className="flex items-center justify-between mb-2">
                      <Label htmlFor="tariff-valid-to">
                        Gültig bis
                        <HelpText>
                          Datum und Uhrzeit, bis wann dieser Tarif gültig ist. Gilt für alle AC/DC-Preise und Blockiergebühren.
                        </HelpText>
                      </Label>
                      <button
                        type="button"
                        onClick={() => {
                          setShowValidUntil(false);
                          setNewTariff({ ...newTariff, validTo: "" });
                        }}
                        className="w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs flex items-center justify-center transition-colors flex-shrink-0"
                        title="Gültig bis entfernen"
                      >
                        ×
                      </button>
                    </div>
                    <Input
                      id="tariff-valid-to"
                      type="datetime-local"
                      value={newTariff.validTo}
                      onChange={(e) =>
                        setNewTariff({ ...newTariff, validTo: e.target.value })
                      }
                      className="mt-auto"
                    />
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Preistypen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-4">
                {!hasACPricing && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setHasACPricing(true);
                      setCurrentPricingType("AC");
                      setActiveTab("ac");
                      setActiveSubTab("pricing");
                    }}
                  >
                    AC Preis hinzufügen
                  </Button>
                )}
                {!hasDCPricing && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setHasDCPricing(true);
                      setCurrentPricingType("DC");
                      setActiveTab("dc");
                      setActiveSubTab("pricing");
                    }}
                  >
                    DC Preis hinzufügen
                  </Button>
                )}
              </div>

              {(hasACPricing || hasDCPricing) && (
                <div className="text-sm text-gray-600">
                  <p>Aktive Preistypen:</p>
                  <ul className="list-disc list-inside">
                    {hasACPricing && <li>AC-Laden (Wechselstrom)</li>}
                    {hasDCPricing && <li>DC-Laden (Gleichstrom)</li>}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {(hasACPricing || hasDCPricing) && (
          <Card>
            <CardHeader>
              <CardTitle>Preiskonfiguration</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-6 bg-gray-100 p-1 rounded-xl shadow-sm">
                  {hasACPricing && (
                    <div className="relative">
                      <TabsTrigger
                        value="ac"
                        onClick={() => setCurrentPricingType("AC")}
                        className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-blue-600 data-[state=active]:font-semibold px-6 py-3 rounded-lg transition-all duration-200 hover:bg-gray-50 flex items-center gap-2"
                      >
                        <span className="text-lg">⚡</span>
                        AC Laden
                      </TabsTrigger>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (window.confirm("Möchten Sie wirklich alle AC-Preise und Blockiergebühren entfernen?")) {
                            removePricingType("AC");
                          }
                        }}
                        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs flex items-center justify-center transition-colors"
                        title="AC Preise entfernen"
                      >
                        ×
                      </button>
                    </div>
                  )}
                  {hasDCPricing && (
                    <div className="relative">
                      <TabsTrigger
                        value="dc"
                        onClick={() => setCurrentPricingType("DC")}
                        className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-purple-600 data-[state=active]:font-semibold px-6 py-3 rounded-lg transition-all duration-200 hover:bg-gray-50 flex items-center gap-2"
                      >
                        <span className="text-lg">🔋</span>
                        DC Laden
                      </TabsTrigger>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (window.confirm("Möchten Sie wirklich alle DC-Preise und Blockiergebühren entfernen?")) {
                            removePricingType("DC");
                          }
                        }}
                        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs flex items-center justify-center transition-colors"
                        title="DC Preise entfernen"
                      >
                        ×
                      </button>
                    </div>
                  )}
                </TabsList>

                {/* AC Tab */}
                {hasACPricing && (
                  <TabsContent value="ac" className="space-y-4">
                    <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
                      <TabsList className="mb-4 bg-blue-50 p-1 rounded-lg border border-blue-200">
                        <TabsTrigger
                          value="pricing"
                          className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm px-4 py-2 rounded-md transition-all duration-200 hover:bg-blue-100 flex items-center gap-2"
                        >
                          <span className="text-sm">💰</span>
                          Preise
                        </TabsTrigger>
                        <TabsTrigger
                          value="blockingFee"
                          className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm px-4 py-2 rounded-md transition-all duration-200 hover:bg-blue-100 flex items-center gap-2"
                        >
                          <span className="text-sm">⏱️</span>
                          Blockiergebühren
                        </TabsTrigger>
                      </TabsList>

                      {/* AC Pricing SubTab */}
                      <TabsContent value="pricing" className="space-y-4">
                        <PriceForm
                          priceForm={acPriceForm}
                          setPriceForm={setACPriceForm}
                          type="AC"
                          showEpexSimulator={showEpexSimulator}
                          setShowEpexSimulator={setShowEpexSimulator}
                        />



                        {/* Übersicht der konfigurierten Preise */}
                        <PriceOverview
                          type="AC"
                          currentPricingType={currentPricingType}
                          acHourlyRates={acHourlyRates}
                          dcHourlyRates={dcHourlyRates}
                          onRemovePrices={removePrices}
                        />

                        {/* Hourly Schedule Grid */}
                        <HourlyScheduleGrid
                          type="AC"
                          hourlyRates={acHourlyRates}
                          selectedHours={selectedHours}
                          setSelectedHours={setSelectedHours}
                          onApplyPrices={applyPriceToSelectedHours}
                          onRemovePrices={() => {
                            const selectedKeys = Object.keys(selectedHours).filter(key => selectedHours[key]);
                            removePrices(selectedKeys);
                          }}
                          mode="pricing"
                        />

                        {/* EPEX Simulator Toggle Button */}
                        {acPriceForm.priceType === "EPEX" && (
                          <div className="flex justify-center">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowEpexSimulator(!showEpexSimulator)}
                              className="flex items-center gap-2"
                            >
                              {showEpexSimulator ? (
                                <>
                                  <span>📊</span>
                                  EPEX Simulator ausblenden
                                </>
                              ) : (
                                <>
                                  <span>📊</span>
                                  EPEX Simulator anzeigen
                                </>
                              )}
                            </Button>
                          </div>
                        )}

                        {/* EPEX Simulator (ganz unten) */}
                        {showEpexSimulator && acPriceForm.priceType === "EPEX" && (
                          <div className="mt-6">
                            <EPEXPriceSimulator
                              marginType={acPriceForm.marginType}
                              marginValue={parseFloat(acPriceForm.marginValue) || 0}
                              fallbackPrice={parseFloat(acPriceForm.fallbackPricePerKwh) || 0}
                              minPrice={parseFloat(acPriceForm.minPricePerKwh) || undefined}
                              maxPrice={parseFloat(acPriceForm.maxPricePerKwh) || undefined}
                              taxesAndLevies={parseFloat(acPriceForm.taxesAndLevies) || 0}
                              sessionFee={parseFloat(acPriceForm.sessionFee) || 0}
                            />
                          </div>
                        )}
                      </TabsContent>

                      {/* AC Blocking Fee SubTab */}
                      <TabsContent value="blockingFee" className="space-y-4">
                        <BlockingFeeForm
                          type="AC"
                          onApplyBlockingFee={applyBlockingFeeToSelectedHours}
                          onRemoveBlockingFees={removeBlockingFees}
                          currentPricingType={currentPricingType}
                          acBlockingFeeSchedules={acBlockingFeeSchedules}
                          dcBlockingFeeSchedules={dcBlockingFeeSchedules}
                          showBlockingFeeSimulator={showBlockingFeeSimulator}
                          setShowBlockingFeeSimulator={setShowBlockingFeeSimulator}
                        />

                        {/* Hourly Schedule Grid for Blocking Fees */}
                        <HourlyScheduleGrid
                          type="AC"
                          hourlyRates={Object.fromEntries(
                            Object.entries(acBlockingFeeSchedules).map(([key, schedule]) => {
                              const parts = key.split('-').map(Number);
                              const hour = parts[1] || 0;
                              return [
                                key,
                                {
                                  hourFrom: hour,
                                  hourTo: hour,
                                  pricePerKwh: schedule.perMinute,
                                  sessionFee: schedule.maxFee,
                                  priceType: "FIXED" as "EPEX" | "FIXED",
                                }
                              ];
                            })
                          )}
                          selectedHours={selectedBlockingHours}
                          setSelectedHours={setSelectedBlockingHours}
                          onApplyPrices={() => {
                            const perMinute = 0.10;
                            const maxFee = 10.00;
                            const gracePeriod = 30;
                            applyBlockingFeeToSelectedHours(perMinute, maxFee, gracePeriod);
                          }}
                          onRemovePrices={() => {
                            const selectedKeys = Object.keys(selectedBlockingHours).filter(key => selectedBlockingHours[key]);
                            removeBlockingFees(selectedKeys);
                          }}
                          mode="blockingFee"
                        />

                        {/* Blocking Fee Simulator Toggle Button */}
                        <div className="flex justify-center">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setShowBlockingFeeSimulator(!showBlockingFeeSimulator)}
                            className="flex items-center gap-2"
                          >
                            {showBlockingFeeSimulator ? (
                              <>
                                <span>⏱️</span>
                                Blockiergebühren Simulator ausblenden
                              </>
                            ) : (
                              <>
                                <span>⏱️</span>
                                Blockiergebühren Simulator anzeigen
                              </>
                            )}
                          </Button>
                        </div>

                        {/* Blockiergebühren Simulator (ganz unten) */}
                        {showBlockingFeeSimulator && (
                          <div className="mt-6">
                            <BlockingFeeSimulator
                              perMinute={0.10}
                              maxFee={10.00}
                              gracePeriod={360}
                              currentPricingType={currentPricingType}
                              acBlockingFeeSchedules={acBlockingFeeSchedules}
                              dcBlockingFeeSchedules={dcBlockingFeeSchedules}
                            />
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </TabsContent>
                )}

                {/* DC Tab */}
                {hasDCPricing && (
                  <TabsContent value="dc" className="space-y-4">
                    <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
                      <TabsList className="mb-4 bg-purple-50 p-1 rounded-lg border border-purple-200">
                        <TabsTrigger
                          value="pricing"
                          className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-sm px-4 py-2 rounded-md transition-all duration-200 hover:bg-purple-100 flex items-center gap-2"
                        >
                          <span className="text-sm">💰</span>
                          Preise
                        </TabsTrigger>
                        <TabsTrigger
                          value="blockingFee"
                          className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-sm px-4 py-2 rounded-md transition-all duration-200 hover:bg-purple-100 flex items-center gap-2"
                        >
                          <span className="text-sm">⏱️</span>
                          Blockiergebühren
                        </TabsTrigger>
                      </TabsList>

                      {/* DC Pricing SubTab */}
                      <TabsContent value="pricing" className="space-y-4">
                        <PriceForm
                          priceForm={dcPriceForm}
                          setPriceForm={setDCPriceForm}
                          type="DC"
                          showEpexSimulator={showEpexSimulator}
                          setShowEpexSimulator={setShowEpexSimulator}
                        />



                        {/* Übersicht der konfigurierten Preise */}
                        <PriceOverview
                          type="DC"
                          currentPricingType={currentPricingType}
                          acHourlyRates={acHourlyRates}
                          dcHourlyRates={dcHourlyRates}
                          onRemovePrices={removePrices}
                        />

                        {/* Hourly Schedule Grid */}
                        <HourlyScheduleGrid
                          type="DC"
                          hourlyRates={dcHourlyRates}
                          selectedHours={selectedHours}
                          setSelectedHours={setSelectedHours}
                          onApplyPrices={applyPriceToSelectedHours}
                          onRemovePrices={() => {
                            const selectedKeys = Object.keys(selectedHours).filter(key => selectedHours[key]);
                            removePrices(selectedKeys);
                          }}
                          mode="pricing"
                        />

                        {/* EPEX Simulator Toggle Button */}
                        {dcPriceForm.priceType === "EPEX" && (
                          <div className="flex justify-center">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowEpexSimulator(!showEpexSimulator)}
                              className="flex items-center gap-2"
                            >
                              {showEpexSimulator ? (
                                <>
                                  <span>📊</span>
                                  EPEX Simulator ausblenden
                                </>
                              ) : (
                                <>
                                  <span>📊</span>
                                  EPEX Simulator anzeigen
                                </>
                              )}
                            </Button>
                          </div>
                        )}

                        {/* EPEX Simulator (ganz unten) */}
                        {showEpexSimulator && dcPriceForm.priceType === "EPEX" && (
                          <div className="mt-6">
                            <EPEXPriceSimulator
                              marginType={dcPriceForm.marginType}
                              marginValue={parseFloat(dcPriceForm.marginValue) || 0}
                              fallbackPrice={parseFloat(dcPriceForm.fallbackPricePerKwh) || 0}
                              minPrice={parseFloat(dcPriceForm.minPricePerKwh) || undefined}
                              maxPrice={parseFloat(dcPriceForm.maxPricePerKwh) || undefined}
                              taxesAndLevies={parseFloat(dcPriceForm.taxesAndLevies) || 0}
                              sessionFee={parseFloat(dcPriceForm.sessionFee) || 0}
                            />
                          </div>
                        )}
                      </TabsContent>

                      {/* DC Blocking Fee SubTab */}
                      <TabsContent value="blockingFee" className="space-y-4">
                        <BlockingFeeForm
                          type="DC"
                          onApplyBlockingFee={applyBlockingFeeToSelectedHours}
                          onRemoveBlockingFees={removeBlockingFees}
                          currentPricingType={currentPricingType}
                          acBlockingFeeSchedules={acBlockingFeeSchedules}
                          dcBlockingFeeSchedules={dcBlockingFeeSchedules}
                          showBlockingFeeSimulator={showBlockingFeeSimulator}
                          setShowBlockingFeeSimulator={setShowBlockingFeeSimulator}
                        />

                        {/* Hourly Schedule Grid for Blocking Fees */}
                        <HourlyScheduleGrid
                          type="DC"
                          hourlyRates={Object.fromEntries(
                            Object.entries(dcBlockingFeeSchedules).map(([key, schedule]) => {
                              const parts = key.split('-').map(Number);
                              const hour = parts[1] || 0;
                              return [
                                key,
                                {
                                  hourFrom: hour,
                                  hourTo: hour,
                                  pricePerKwh: schedule.perMinute,
                                  sessionFee: schedule.maxFee,
                                  priceType: "FIXED" as "EPEX" | "FIXED"
                                  ,
                                }
                              ];
                            })
                          )}
                          selectedHours={selectedBlockingHours}
                          setSelectedHours={setSelectedBlockingHours}
                          onApplyPrices={() => {
                            const perMinute = 0.10;
                            const maxFee = 10.00;
                            const gracePeriod = 30;
                            applyBlockingFeeToSelectedHours(perMinute, maxFee, gracePeriod);
                          }}
                          onRemovePrices={() => {
                            const selectedKeys = Object.keys(selectedBlockingHours).filter(key => selectedBlockingHours[key]);
                            removeBlockingFees(selectedKeys);
                          }}
                          mode="blockingFee"
                        />

                        {/* Blocking Fee Simulator Toggle Button */}
                        <div className="flex justify-center">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setShowBlockingFeeSimulator(!showBlockingFeeSimulator)}
                            className="flex items-center gap-2"
                          >
                            {showBlockingFeeSimulator ? (
                              <>
                                <span>⏱️</span>
                                Blockiergebühren Simulator ausblenden
                              </>
                            ) : (
                              <>
                                <span>⏱️</span>
                                Blockiergebühren Simulator anzeigen
                              </>
                            )}
                          </Button>
                        </div>

                        {/* Blockiergebühren Simulator (ganz unten) */}
                        {showBlockingFeeSimulator && (
                          <div className="mt-6">
                            <BlockingFeeSimulator
                              perMinute={0.10}
                              maxFee={10.00}
                              gracePeriod={360}
                              currentPricingType={currentPricingType}
                              acBlockingFeeSchedules={acBlockingFeeSchedules}
                              dcBlockingFeeSchedules={dcBlockingFeeSchedules}
                            />
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </TabsContent>
                )}
              </Tabs>
            </CardContent>
          </Card>
        )}

        {/* Save Button */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {isAddingPriceMode ? (
                  <span>
                    Neue {priceType}-{mode === 'price' ? 'Preise' : 'Blockiergebühren'} werden zu <strong>{existingTariffName || `Tarif (${tariffId})`}</strong> hinzugefügt
                  </span>
                ) : activeMandant ? (
                  <span>Tarif wird für Mandant <strong>{activeMandant.name}</strong> erstellt</span>
                ) : (
                  <span className="text-red-600">Kein Mandant ausgewählt</span>
                )}
              </div>
              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (isAddingPriceMode) {
                      router.push(`/tariff/${tariffId}`);
                    } else {
                      router.push("/tariff");
                    }
                  }}
                  disabled={isLoading}
                >
                  Abbrechen
                </Button>
                <Button
                  type="button"
                  onClick={handleSaveTariff}
                  disabled={isLoading || (!isAddingPriceMode && !activeMandant) || (!hasACPricing && !hasDCPricing)}
                  className="btn-primary"
                >
                  {isLoading
                    ? "Speichern..."
                    : isAddingPriceMode
                      ? `${priceType}-${mode === 'price' ? 'Preise' : 'Blockiergebühren'} hinzufügen`
                      : "Tarif speichern"
                  }
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
