// Data mapping utilities for converting form data to Strapi API format

import { euroStringToCent, euroStringToEuro } from "./currencyConversion";
import type {
  HourlyRate,
  BlockingFeeSchedule,
  PriceFormData,
  TariffCreateData,
  PriceComponentData,
  DailyScheduleData,
  HourlyRateData,
  BlockFeeComponentData,
  BlockFeeScheduleData
} from "../types/tariff.types";

// Day mapping from index to Strapi enum
const DAY_MAPPING = [
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
  "sunday"
] as const;

/**
 * Convert form hourly rates to Strapi daily schedules format
 */
export function convertHourlyRatesToDailySchedules(
  hourlyRates: { [key: string]: HourlyRate },
  validFrom: string,
  validTo: string | null,
  userDocumentId: string
): PriceComponentData {
  // Group hourly rates by day
  const dayGroups: { [day: number]: HourlyRate[] } = {};

  Object.entries(hourlyRates).forEach(([key, rate]) => {
    const parts = key.split('-');
    const dayStr = parts[0];
    const hourStr = parts[1];

    if (dayStr && hourStr) {
      const day = parseInt(dayStr);
      const hour = parseInt(hourStr);

      if (!isNaN(day) && !isNaN(hour)) {
        if (!dayGroups[day]) {
          dayGroups[day] = [];
        }

        dayGroups[day].push({
          ...rate,
          hourFrom: hour,
          hourTo: hour === 23 ? 23 : hour + 1 // Handle hour 23 edge case (max 23)
        });
      }
    }
  });

  // Convert to daily schedules
  const dailySchedules: DailyScheduleData[] = [];

  Object.entries(dayGroups).forEach(([dayStr, rates]) => {
    const day = parseInt(dayStr);
    if (day >= 0 && day < DAY_MAPPING.length) {
      const dayOfWeek = DAY_MAPPING[day];
      if (dayOfWeek) {
        // Sort rates by hour
        const sortedRates = rates.sort((a, b) => a.hourFrom - b.hourFrom);

        // Convert to API format with proper data types
        const hourlyRateData: HourlyRateData[] = sortedRates.map(rate => ({
          hourFrom: rate.hourFrom,
          hourTo: rate.hourTo,
          // Decimal fields (Euro)
          pricePerKwh: rate.pricePerKwh,
          sessionFee: rate.sessionFee,
          fallbackPricePerKwh: rate.fallbackPricePerKwh,
          marginValue: rate.marginValue,
          // Integer fields (Cent) - already converted in frontend components
          minPricePerKwh: rate.minPricePerKwh,
          maxPricePerKwh: rate.maxPricePerKwh,
          // BigInteger field (Cent) - already converted in frontend components
          taxesAndLevies: rate.taxesAndLevies,
          // Enum and other fields
          priceType: rate.priceType,
          marginType: rate.marginType,
          epexAveragingPeriodHours: rate.epexAveragingPeriodHours
        }));

        dailySchedules.push({
          dayOfWeek,
          hourlyRate: hourlyRateData
        });
      }
    }
  });

  return {
    validFrom,
    validTo,
    dailySchedules,
    changeDate: new Date().toISOString(),
    users_permissions_user: userDocumentId
  };
}

/**
 * Convert form blocking fee schedules to Strapi format
 */
export function convertBlockingFeesToSchedules(
  blockingFees: { [key: string]: BlockingFeeSchedule },
  validFrom: string,
  validTo: string | null,
  userDocumentId: string
): BlockFeeComponentData {
  // Group blocking fees by day
  const dayGroups: { [day: number]: Array<{ hour: number; schedule: BlockingFeeSchedule }> } = {};

  Object.entries(blockingFees).forEach(([key, schedule]) => {
    const parts = key.split('-');
    const dayStr = parts[0];
    const hourStr = parts[1];

    if (dayStr && hourStr) {
      const day = parseInt(dayStr);
      const hour = parseInt(hourStr);

      if (!isNaN(day) && !isNaN(hour)) {
        if (!dayGroups[day]) {
          dayGroups[day] = [];
        }

        dayGroups[day].push({ hour, schedule });
      }
    }
  });

  // Convert to block fee schedules
  const blockFeeSchedules: BlockFeeScheduleData[] = [];

  Object.entries(dayGroups).forEach(([dayStr, schedules]) => {
    const day = parseInt(dayStr);
    if (day >= 0 && day < DAY_MAPPING.length) {
      const dayOfWeek = DAY_MAPPING[day];
      if (dayOfWeek) {
        // Sort by hour
        const sortedSchedules = schedules.sort((a, b) => a.hour - b.hour);

        // Group consecutive hours with same settings
        let currentGroup: {
          startHour: number;
          endHour: number;
          schedule: BlockingFeeSchedule
        } | null = null;

        for (const { hour, schedule } of sortedSchedules) {
          if (currentGroup &&
              currentGroup.schedule.perMinute === schedule.perMinute &&
              currentGroup.schedule.maxFee === schedule.maxFee &&
              currentGroup.schedule.gracePeriod === schedule.gracePeriod &&
              currentGroup.endHour + 1 === hour) {
            // Extend current group
            currentGroup.endHour = hour;
          } else {
            // Save previous group if exists
            if (currentGroup) {
              blockFeeSchedules.push({
                dayOfWeek,
                startHour: currentGroup.startHour,
                endHour: currentGroup.endHour,
                // Decimal fields (Euro values) - based on schema
                perMinute: currentGroup.schedule.perMinute,
                maxFee: currentGroup.schedule.maxFee,
                // Integer field
                gracePeriod: currentGroup.schedule.gracePeriod
              });
            }

            // Start new group
            currentGroup = {
              startHour: hour,
              endHour: hour,
              schedule
            };
          }
        }

        // Save last group
        if (currentGroup) {
          blockFeeSchedules.push({
            dayOfWeek,
            startHour: currentGroup.startHour,
            endHour: currentGroup.endHour,
            // Decimal fields (Euro values) - based on schema
            perMinute: currentGroup.schedule.perMinute,
            maxFee: currentGroup.schedule.maxFee,
            // Integer field
            gracePeriod: currentGroup.schedule.gracePeriod
          });
        }
      }
    }
  });

  return {
    validFrom,
    validTo,
    blockFeeSchedule: blockFeeSchedules,
    changeDate: new Date().toISOString(),
    users_permissions_user: userDocumentId
  };
}

/**
 * Convert complete form data to Strapi tariff format
 */
export function convertFormDataToTariffData(
  tariffName: string,
  mandantDocumentId: string,
  userDocumentId: string,
  hasACPricing: boolean,
  hasDCPricing: boolean,
  acHourlyRates: { [key: string]: HourlyRate },
  dcHourlyRates: { [key: string]: HourlyRate },
  acBlockingFees: { [key: string]: BlockingFeeSchedule },
  dcBlockingFees: { [key: string]: BlockingFeeSchedule },
  acValidFrom: string,
  acValidTo: string | null,
  dcValidFrom: string,
  dcValidTo: string | null
): TariffCreateData {
  const tariffData: TariffCreateData = {
    name: tariffName,
    mandants: [mandantDocumentId]
  };

  // Add AC pricing if configured
  if (hasACPricing && Object.keys(acHourlyRates).length > 0) {
    tariffData.priceAC = [convertHourlyRatesToDailySchedules(
      acHourlyRates,
      acValidFrom,
      acValidTo,
      userDocumentId
    )];
  }

  // Add DC pricing if configured
  if (hasDCPricing && Object.keys(dcHourlyRates).length > 0) {
    tariffData.priceDC = [convertHourlyRatesToDailySchedules(
      dcHourlyRates,
      dcValidFrom,
      dcValidTo,
      userDocumentId
    )];
  }

  // Add AC blocking fees if configured
  if (hasACPricing && Object.keys(acBlockingFees).length > 0) {
    tariffData.blockFeeAC = [convertBlockingFeesToSchedules(
      acBlockingFees,
      acValidFrom,
      acValidTo,
      userDocumentId
    )];
  }

  // Add DC blocking fees if configured
  if (hasDCPricing && Object.keys(dcBlockingFees).length > 0) {
    tariffData.blockFeeDC = [convertBlockingFeesToSchedules(
      dcBlockingFees,
      dcValidFrom,
      dcValidTo,
      userDocumentId
    )];
  }

  return tariffData;
}
