// backend/src/api/payter/controllers/terminal/ui-controller.ts
import screens from "../../config/payter-screens.json";

import { startCardReading, authorizePayment, getCardInfo } from './payment-controller';
import {PayterErrorResponse, ResponseData} from "../../types/types";
import {displayScreen} from "./uiActions";
import { checkActiveChargingSession, stopChargingSession } from '../../../terminal/services/charging-session-checker';

/**
 * Implementiert die State Machine für UI-Callbacks (Button-Klicks, Screen-Interaktionen)
 */
async function processUiStateMachine(ctx, screenId: string, buttonId: string) {
  // State Machine implementieren
  let nextScreen: string | null = null;
  let sessionId: string = ctx.request.body?.sessionId || `session_${Date.now()}`;

  // Stelle sicher, dass sessionId immer ein String ist
  if (typeof sessionId !== 'string') {
    sessionId = `session_${Date.now()}`;
  }

  const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

  console.log(`Current state: Screen=${screenId}, Button=${buttonId}`);

  switch (screenId) {
    case 'screen-init':
      if (buttonId === 'ok' || buttonId === 'buttons.ok') {
        // Von Init zu CHARGING_POINTS
        nextScreen = 'screen-list-charging-points';
        console.log('Transition: IDLE -> CHARGING_POINTS');
        break;
      } else if (buttonId === 'lang') {
        // Von Init zu CHARGING_POINTS
        nextScreen = 'screen-language';
        console.log('Transition: IDLE -> CHARGING_POINTS');
        break;
      }
      nextScreen = 'screen-init';
      break;
    case 'screen-language':
      if (ctx.request.body.response) {
        ctx.state.terminal = await strapi.documents('api::terminal.terminal').update({
          documentId: ctx.state.terminal.documentId,
          data: {
            currentLanguage: ctx.request.body.response
          }
        });
      }

      nextScreen = 'screen-init';
      break;
    case 'screen-list-charging-points':
      // Prüfen, ob ein Ladepunkt ausgewählt wurde oder eine andere Aktion
      if (buttonId) {
        // Prüfen, ob es sich um eine EVSE-ID handelt (nicht 'cancel')
        if (buttonId !== 'cancel') {
          ctx.state.selectedChargingPoint = buttonId;

          // Prüfe, ob an dieser EVSE bereits ein aktiver Ladevorgang läuft
          const { hasActiveSession, ocpiSession } = await checkActiveChargingSession(buttonId);

          if (hasActiveSession) {
            // Speichere die aktive Payment-Session für später
            ctx.state.activePaymentSession = ocpiSession.payment_session;
            ctx.state.ocpiSession = ocpiSession;
            // Zeige den Stop-Charging-Screen an
            nextScreen = 'screen-stop-charging';
            console.log('Transition: CHARGING_POINTS -> STOP_CHARGING (active session found)');
          } else {
            // Von CHARGING_POINTS zu PRICE
            nextScreen = 'show-price-for-evse';
            console.log('Transition: CHARGING_POINTS -> PRICE (no active session)');
          }
        } else {
          // Zurück zum Idle-Screen
          nextScreen = 'screen-init';
          console.log('Transition: CHARGING_POINTS -> IDLE');
        }
      }
      break;
    case 'show-price-for-evse':
      // Auf der Preisübersicht wurde ein Button gedrückt
      if (buttonId === 'ok') {
        // Benutzer möchte bezahlen, starte den Kartenlesevorgang
        try {
          // Hole die Preisinformationen aus der Session
          const authorizedAmount = 5000; // Gesamtbetrag in Cent

          // Parameter für den Callback vorbereiten
          const callbackParams = new URLSearchParams();

          // evseDocumentId hinzufügen, wenn vorhanden
          const evseId = ctx.request.query.evseDocumentId || ctx.state.selectedEvse?.documentId || '';
          if (evseId) {
            callbackParams.set('evseDocumentId', evseId);
          }

          callbackParams.set('datetimeForShowPrice', ctx.request.query.datetimeForShowPrice)

          // Callback-URL mit Parametern erstellen
          const callbackUrlWithEvseId = `${callback.cardInfo}${callbackParams.toString() ? `?${callbackParams.toString()}` : ''}`;

          // Starte den Kartenlesevorgang über die API
          await startCardReading(ctx, apiClient, callbackUrlWithEvseId, authorizedAmount);

          // Kein nächster Screen, da der Kartenlesevorgang über die API gestartet wird
          // Das Terminal wird automatisch in den Kartenlesemodus wechseln
          console.log('Started card reading via API');

          // Erstelle eine Antwort, die dem ResponseData-Interface entspricht
          const cardReadingResponse: ResponseData = {
            status_code: 1000,
            status_message: `OK - Started card reading for terminal ${ctx.state.terminal.serialNumber}`,
            session_id: sessionId,
            next_screen: 'card_reading' // Verwende einen Screen-Namen als next_screen
          };

          // Füge ein zusätzliches Feld hinzu (nicht im Interface definiert)
          const responseWithAction = {
            ...cardReadingResponse,
            next_action: 'card_reading_started'
          };

          return ctx.send(responseWithAction);
        } catch (error) {
          console.error(`Error starting card reading for terminal ${ctx.state.terminal.serialNumber}:`, error);
          // Bei einem Fehler zum Error-Screen wechseln
          nextScreen = 'error';
          console.log('Transition: PRICE -> ERROR (card reading failed)');
        }
      } else if (buttonId === 'cancel') {
        // Zurück zum Ladepunkte-Screen
        nextScreen = 'screen-init';
        console.log('Transition: PRICE -> CHARGING_POINTS');
      }
      break;
    case 'screen-reading':
      // Dieser Fall wird nicht mehr benötigt, da der Kartenlesevorgang über die API gestartet wird
      // Wir behalten ihn für den Fall, dass wir später wieder auf den Screen-basierten Ansatz zurückwechseln
      console.log('Received callback for screen-reading, but card reading is now handled via API');
      break;

      // Callbacks vom Kartenleseprozess (nicht screen-basiert)
      // Diese werden vom Terminal gesendet, wenn der Kartenlesevorgang über die API gestartet wurde
    case undefined:
    case null:
      // Für UI-Callbacks ohne Screen-ID verwenden wir die Fallback-Logik
      if (buttonId === 'card_success' || buttonId === 'success') {
        // Karte wurde erfolgreich gelesen (alte Logik als Fallback)
        nextScreen = 'charging-started';
        console.log('Transition: CARD_READING -> CHARGING_STARTED (fallback)');
      } else if (buttonId === 'card_error' || buttonId === 'error') {
        // Fehler beim Lesen der Karte
        nextScreen = 'error';
        console.log('Transition: CARD_READING -> ERROR');
      }
      break;

    case 'screen-charging-started':
      if (buttonId === 'ok') {
        // Zurück zum Idle-Screen
        nextScreen = 'screen-init';
        console.log('Transition: CHARGING_STARTED -> IDLE');
      }
      break;

    case 'screen-stop-charging':
      if (buttonId !== 'cancel') {
        // Benutzer möchte den Ladevorgang stoppen
        const { hasActiveSession, ocpiSession } = await checkActiveChargingSession(buttonId);
        try {
          if (hasActiveSession && ocpiSession) {
            const stopResult = await stopChargingSession(ocpiSession);

            if (stopResult.success) {
              // Erfolgreich gestoppt, zurück zum Idle-Screen
              nextScreen = 'screen-init';
              console.log('Transition: STOP_CHARGING -> IDLE (session stopped successfully)');
            } else {
              // Fehler beim Stoppen, zeige Error-Screen
              nextScreen = 'screen-error';
              console.log('Transition: STOP_CHARGING -> ERROR (failed to stop session)');
            }
          } else {
            // Keine aktive Session gefunden, zurück zum Idle-Screen
            nextScreen = 'screen-init';
            console.log('Transition: STOP_CHARGING -> IDLE (no active session)');
          }
        } catch (error) {
          console.error('Error stopping charging session:', error);
          nextScreen = 'screen-error';
          console.log('Transition: STOP_CHARGING -> ERROR (exception)');
        }
      } else if (buttonId === 'cancel') {
        // Benutzer möchte nicht stoppen, zurück zum Idle-Screen
        nextScreen = 'screen-init';
        console.log('Transition: STOP_CHARGING -> IDLE (cancelled)');
      }
      break;

    case 'screen-rfid-roaming-confirm':
      if (buttonId === 'ok') {
        // Benutzer hat "Ja" geklickt - starte RFID Roaming Session
        try {
          console.log('User confirmed RFID roaming - starting OCPI session');
          const rfidTag = ctx.request.query.rfidTag;
          const evseDocumentId = ctx.request.query.evseDocumentId;



        } catch (error) {
          console.error('Error starting RFID roaming session:', error);
          nextScreen = 'screen-error';
          console.log('Transition: RFID_CONFIRM -> ERROR (exception)');
        }
      } else if (buttonId === 'cancel') {
        // Benutzer hat "Nein" geklickt - lösche die temporären Daten und zurück zum Idle-Screen
        nextScreen = 'screen-init';
        console.log('Transition: RFID_CONFIRM -> IDLE (cancelled)');
      }
      break;

    case 'screen-error':
      if (buttonId === 'ok') {
        // Erneut versuchen
        nextScreen = 'screen-init';
        console.log('Transition: ERROR -> READING');
      }
      break;

    default:
      // Unbekannter Screen, zurück zum Idle-Screen
      nextScreen = 'screen-init';
  }

  // Nächsten Screen anzeigen, falls vorhanden
  if (nextScreen) {
    let response;

    // Je nach nächstem Screen die entsprechende API aufrufen
    switch (nextScreen) {
      case 'screen-init':
        await displayScreen(ctx, apiClient, callback.ui, 'screen-init');
        break;

      case 'screen-list-charging-points':
        response = await displayScreen(ctx, apiClient, callback.ui, 'screen-list-charging-points');
        break;

      case 'screen-language':
        response = await displayScreen(ctx, apiClient, callback.ui, 'screen-language');
        break;

      case 'show-price-for-evse':
        // Hole die EVSE-Informationen aus der Datenbank
        const evse = await strapi.documents('api::ocpi-evse.ocpi-evse').findOne({
          documentId: ctx.state.selectedChargingPoint,
          populate: ['location']
        });

        if (!evse) {
          console.error(`EVSE with documentId ${buttonId} not found`);
          nextScreen = 'error';
          break;
        }

        const date = new Date();
        const isoDate = date.toISOString()

        const tariffService = strapi.service('api::tariff.tariff');
        const priceInfo = await tariffService.getTariff(buttonId, date);

        // Formatiere die Preise für die Anzeige (mit Komma statt Punkt)
        const formattedPrice = (priceInfo.pricePerKwh).toFixed(2).replace('.', ',');
        const formattedSessionFee = (priceInfo.sessionFee / 100).toFixed(2).replace('.', ',');
        const formattedPerMinute = (priceInfo.perMinute / 100).toFixed(2).replace('.', ',');
        const formattedMaxBlockFee = priceInfo.maxBlockFee;
        const gracePeriodInMin = priceInfo.gracePeriod || 0;

        const priceScreen = screens.screens[ctx.state.terminal.currentLanguage].find(screen => screen.id === 'show-price-for-evse');
        if (priceScreen) {
          // Ersetze die Platzhalter im Message-Text
          const screenWithPrice = JSON.parse(JSON.stringify(priceScreen));

          // Setze den Titel mit dem Ladepunkt-Namen
          if (screenWithPrice.properties.title) {
            screenWithPrice.properties.title = evse.labelForTerminal || evse.evseId || 'Ladepunkt';
          }


          // Erstelle die Nachricht basierend auf den vorhandenen Preiskomponenten
          let message = `${formattedPrice}€/kWh\n`;

          // Startgebühr nur anzeigen, wenn sie größer als 0 ist
          if (priceInfo.sessionFee > 0) {
            message += `${priceScreen.properties.sessionFeeLabel} ${formattedSessionFee}€\n`;
          } else {
            message += '\n';
          }

          // Blockiergebühr nur anzeigen, wenn sie größer als 0 ist
          if (priceInfo.perMinute > 0) {
            message += `\n${priceScreen.properties.blockingFeeFromLabel}:\n ${gracePeriodInMin}min ${formattedPerMinute}€${priceScreen.properties.blockingFeePerMinuteLabel}\n`;



            // Max Blockiergebühr nur anzeigen, wenn sie größer als 0 ist
            if (priceInfo.maxBlockFee > 0) {
              message += `max ${priceScreen.properties.blockingFeeLabel} ${formattedMaxBlockFee}€`;
            }
          }

          screenWithPrice.properties.message = message;


          // Erstelle die Basis-URL
          const baseUrl = `/terminals/${ctx.state.terminal.serialNumber}/ui`;
          const uiParams = new URLSearchParams();
          uiParams.set('evseDocumentId', evse.documentId);
          uiParams.set('datetimeForShowPrice', isoDate);

          const callbackUrl = `${callback.ui}${uiParams.toString() ? `?${uiParams.toString()}` : ''}`;
          const apiParams = new URLSearchParams({ callbackUrl });
          response = await apiClient.post(`${baseUrl}?${apiParams.toString()}`, screenWithPrice);
        }
        break;

      case 'charging-started':

        break;

      case 'screen-stop-charging':



        response = await displayScreen(ctx, apiClient, callback.ui, 'screen-stop-charging');
        break;

      case 'error':
        response = await displayScreen(ctx, apiClient, callback.ui, 'screen-error');
        break;
    }

    // Bereite die Antwort vor
    const nextScreenStr: string = nextScreen || '';

    const responseData: ResponseData = {
      status_code: 1000,
      status_message: `OK - Transitioned to ${nextScreenStr} state`,
      session_id: sessionId,
      next_screen: nextScreenStr
    };

    return ctx.send(responseData);
  }

  // Wenn kein nächster Screen definiert ist, einfach OK zurückgeben
  const noTransitionResponse: ResponseData = {
    status_code: 1000,
    status_message: "OK - Callback processed, no state transition",
    session_id: sessionId,
    next_screen: ""
  };

  return ctx.send(noTransitionResponse);
}


/**
 * Verarbeitet UI-Callbacks vom Payter-Terminal (z.B. Button-Klicks).
 */
export async function uiCallback(ctx) {
  console.log("Received UI callback from Payter:", ctx.request.body);

  // Extrahiere relevante Daten aus dem Callback
  const callbackData = ctx.request.body || {};
  const serialNumber = callbackData.serialNumber;
  const screenId = callbackData.screenId;
  const buttonId = callbackData.response;

  console.log('Extracted UI callback data:', { serialNumber, screenId, buttonId });

  if (!serialNumber) {
    return ctx.send({
      status: 400,
      message: "Bad Request: Missing 'terminalId' in callback data."
    });
  }

  const terminal = await strapi.documents('api::terminal.terminal').findFirst({
    filters: {
      serialNumber: serialNumber
    },
    populate: ['mandant']
  });
  ctx.state.terminal = terminal;

  try {

    return await processUiStateMachine(ctx, screenId, buttonId);
  } catch (error) {
    console.error(`Error processing UI callback for terminal ${terminal.serialNumber}:`, error);
    ctx.status = 500;

    // Erstelle die Fehlerantwort
    const errorResponse: PayterErrorResponse = {
      status: 500,
      message: `Error processing UI callback for terminal ${ctx.state.terminal.serialNumber}`,
      error: error.message || 'Unknown error',
      timestamp: new Date().toISOString()
    };

    return ctx.send(errorResponse);
  }
}
