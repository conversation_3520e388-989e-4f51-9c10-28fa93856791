import screens from "../../config/payter-screens.json";
import {displayScreen} from "./uiActions";

const AID_RFID_CARD = "080100"



export async function cardInfoCallback(ctx) {

    const serialNumber = ctx.request.body.serialNumber;
    const evseDocumentId = ctx.request.query.evseDocumentId;

    const terminal = await strapi.documents('api::terminal.terminal').findFirst({
        filters: {
            serialNumber: {
                $eq: serialNumber
            }
        },
        populate: ['mandant']
    });

    if (!terminal) {
        return ctx.send({
            status_code: 3000,
            status_message: "Terminal not found",
            session_id: ctx.request.body.sessionId,
            next_screen: ""
        });
    }
    ctx.state.terminal = terminal;
    const { apiClient,callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

    const {aid,cardId} = ctx.request.body;

    if(aid == AID_RFID_CARD)
    {
        // Erstelle eine temporäre Payment-Session, um die RFID-Daten zu persistieren
        const tempPaymentSession = await strapi.documents('api::payment-session.payment-session').create({
            data: {
                paymentIntent: `rfid_temp_${Date.now()}`,
                terminal: terminal.documentId,
                mandant: terminal.mandant?.documentId,
                ocpi_evse: evseDocumentId,
                cardId: cardId,
                paymentSessionState: 'started',
                blockedAmount: 0, // Temporäre Session, kein Betrag blockiert
                history: JSON.stringify([{
                    type: 'rfid_detected',
                    cardId: cardId,
                    aid: aid,
                    timestamp: new Date().toISOString(),
                    evseDocumentId: evseDocumentId
                }])
            }
        });

        console.log(`Created temporary payment session ${tempPaymentSession.documentId} for RFID card ${cardId}`);

        // Zeige den RFID Roaming Bestätigungsscreen
        ctx.state.rfidTag = cardId;
        ctx.state.evseDocumentId = evseDocumentId;
        ctx.state.tempPaymentSessionId = tempPaymentSession.documentId;

        const response = await displayScreen(ctx, apiClient, callback.ui, 'screen-rfid-roaming-confirm');
        return ctx.send({
            status_code: 1000,
            status_message: "RFID card detected - showing confirmation screen",
            session_id: ctx.request.body.sessionId,
            next_screen: "screen-rfid-roaming-confirm"
        });


        // Der ursprüngliche Code wird später durch die Benutzerantwort ausgelöst
        /*
        try {
            // Rufe den OCPI-Command-Service auf, um eine Ladesession zu starten
            const ocpiResult = await strapi.service('api::ocpi-command.ocpi-command').startSession({
                evse_id: ctx.request.query.evseDocumentId,
                mandant_id: terminal.mandant?.documentId,
                payment_intent: "",
                serialNumber: terminal.serialNumber,
                rfidTag: cardId,
            });

            if (ocpiResult.success) {
                console.log('OCPI session started successfully:', ocpiResult.response.data);

            } else {
                console.error('Failed to start OCPI session:', ocpiResult.response);


                //await cancelPaymentSession(paymentSession.documentId);
            }
        } catch (error) {
            console.error('Error starting OCPI session:', error);
        }
        */

    }
    else {

        // Berechne das Datum 3 Tage in der Zukunft
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 10);

        // Formatiere das Datum als YYYY-MM-DD HH:MM:SS
        const formattedDate = futureDate.toISOString().slice(0, 10) + ' ' +
            futureDate.toTimeString().slice(0, 8);

        const callbackParams = new URLSearchParams();
        callbackParams.set('datetimeForShowPrice', ctx.request.query.datetimeForShowPrice)
        callbackParams.set('evseDocumentId', evseDocumentId);
        const publicURL = strapi.config.get('server.publicURL');
        const authorizedCallbackUrl = `${publicURL}/api/payter/stateCallback?${callbackParams.toString()}`;

        // Parameter für die Autorisierung
        const params = new URLSearchParams({
            uiMessage: 'Vielen Dank für Ihre Zahlung, der Ladevorgang wird gestartet...',
            uiMessageTimeout: '15',
            receiptLine: 'Eulektro Ladevorgang',
            callbackUrl: authorizedCallbackUrl,
            merchantReference: evseDocumentId,
            sessionActionDate: formattedDate,
            sessionAction: 'CANCEL'
        });

        const url = `/terminals/${serialNumber}/authorize?${params.toString()}`;

        console.log(`Authorizing payment for terminal ${serialNumber} with URL: ${url}`);

        try {
            // Sende die Anfrage an die Payter API mit leerem Objekt statt leerem String
            const response = await apiClient.post(url, {});
            return response.data;
        } catch (error) {
            console.error(`Error authorizing payment for terminal ${serialNumber}:`, error);
            throw error;
        }
    }

}
