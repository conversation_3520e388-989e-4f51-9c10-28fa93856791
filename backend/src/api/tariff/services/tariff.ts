/**
 * tariff service
 */

import { factories } from '@strapi/strapi';

interface Price {
  sessionFee: number;
  pricePerKwh: number;
  perMinute: number;
  gracePeriod: number;
  maxBlockFee: number;
  // EPEX-related fields
  isEPEXPricing?: boolean;
  epexBasePrice?: number; // EUR/kWh - raw EPEX price
  epexCalculatedPrice?: number; // EUR/kWh - final calculated price with margins/taxes
  fallbackUsed?: boolean;
  priceBreakdown?: {
    epexBase?: number;
    taxesAndLevies?: number;
    margin?: number;
    finalPrice?: number;
  };
}

// Interface für einen OCPI Connector
interface OCPIConnector {
  id?: string;
  standard?: string;
  format?: string;
  powerType?: string;
  voltage?: number;
  amperage?: number;
  tariffId?: string;
  [key: string]: any; // Für andere mögliche Eigenschaften
}

// Interface for Strapi 5 tariff structure
interface TariffComponent {
  validFrom: string;
  validTo: string | null;
  dailySchedules: Array<{
    dayOfWeek: string;
    hourlyRate: Array<{
      hourFrom: number;
      hourTo?: number;
      pricePerKwh: number;
      sessionFee: number;
      priceType: string;
      // EPEX-specific fields
      fallbackPricePerKwh?: number;
      minPricePerKwh?: number; // in cents
      maxPricePerKwh?: number; // in cents
      marginType?: string;
      marginValue?: number;
      taxesAndLevies?: number; // in cents
      epexAveragingPeriodHours?: number;
    }>;
  }>;
}

interface BlockFeeComponent {
  validFrom: string;
  validTo: string | null;
  blockFeeSchedule: Array<{
    dayOfWeek: string;
    startHour: number;
    endHour: number;
    perMinute: number;
    maxFee: number;
    gracePeriod?: number;
  }>;
}

export default factories.createCoreService('api::tariff.tariff', ({strapi}) => ({
  /**
   * Get tariff with full details including the tariff object
   * This method returns both the pricing information and the tariff object
   * Used when we need to store the tariff relation for later use
   */
  async getTariffWithDetails(evseDocumentId: string, datetimeForPrice: Date): Promise<{
    price: Price;
    tariff: any | null;
  }> {
    const defaultResult = {
      price: {
        sessionFee: 0,
        pricePerKwh: 0,
        perMinute: 0,
        gracePeriod: 0,
        maxBlockFee: 0
      },
      tariff: null
    };

    try {
      // Get the EVSE by uid
      const evse = await strapi.documents('api::ocpi-evse.ocpi-evse').findOne({
        documentId: evseDocumentId,
        populate: {
          terminals: true,
          location: {
            populate: {
              mandant: true
            }
          }
        }
      });

      if (!evse) {
        console.log(`EVSE not found for documentId: ${evseDocumentId}`);
        return defaultResult;
      }

      const currentDay = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][datetimeForPrice.getDay()];
      const currentHour = datetimeForPrice.getHours();

      // Determine charger type (AC/DC) from connectors
      let chargerType: 'AC' | 'DC' = 'AC';
      if (evse.connectors && Array.isArray(evse.connectors)) {
        for (const connector of evse.connectors) {
          const typedConnector = connector as unknown as OCPIConnector;
          if (typedConnector.powerType && typeof typedConnector.powerType === 'string' &&
              typedConnector.powerType.startsWith('DC')) {
            chargerType = 'DC';
            break;
          }
        }
      }

      // Helper function to find tariffs with proper population
      const findTariffWithPopulation = async (whereClause: any) => {
        return await strapi.db.query('api::tariff.tariff').findOne({
          where: {
            ...whereClause,
            deleted: { $ne: true }
          },
          populate: {
            priceAC: {
              populate: {
                dailySchedules: {
                  populate: {
                    hourlyRate: true
                  }
                }
              }
            },
            priceDC: {
              populate: {
                dailySchedules: {
                  populate: {
                    hourlyRate: true
                  }
                }
              }
            },
            blockFeeAC: {
              populate: {
                blockFeeSchedule: true
              }
            },
            blockFeeDC: {
              populate: {
                blockFeeSchedule: true
              }
            }
          }
        });
      };

      // Priority 1: Check if tariff is linked directly to EVSE
      let tariff = await findTariffWithPopulation({
        ocpi_evses: { documentId: evse.documentId }
      });

      // Priority 2: Check if tariff is linked to terminal
      if (!tariff && evse.terminals && evse.terminals.length > 0) {
        const terminal = evse.terminals[0];
        tariff = await findTariffWithPopulation({
          terminals: { documentId: terminal.documentId }
        });
      }

      // Priority 3: Check if tariff is linked to location
      if (!tariff && evse.location) {
        tariff = await findTariffWithPopulation({
          ocpi_locations: { documentId: evse.location.documentId }
        });
      }

      // Priority 4: Check if tariff is linked to mandant
      if (!tariff && evse.location?.mandant) {
        tariff = await findTariffWithPopulation({
          mandants: { documentId: evse.location.mandant.documentId }
        });
      }

      // If no tariff found, return default result
      if (!tariff) {
        console.log(`No tariff found for EVSE ${evseDocumentId}, charger type: ${chargerType}`);
        return defaultResult;
      }

      // Calculate the price using the found tariff
      const price = await this.calculatePriceFromTariff(tariff, chargerType, datetimeForPrice);

      return {
        price,
        tariff
      };

    } catch (error) {
      console.error('Error in getTariffWithDetails service:', error);
      return defaultResult;
    }
  },

  /**
   * Calculate price from a tariff object and datetime
   * This is a helper method used by both getTariff and getTariffWithDetails
   */
  async calculatePriceFromTariff(tariff: any, chargerType: 'AC' | 'DC', datetimeForPrice: Date): Promise<Price> {
    const defaultPrice: Price = {
      sessionFee: 0,
      pricePerKwh: 0,
      perMinute: 0,
      gracePeriod: 0,
      maxBlockFee: 0
    };

    try {
      const currentDay = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][datetimeForPrice.getDay()];
      const currentHour = datetimeForPrice.getHours();

      // Helper function to find valid price component for the given datetime
      const findValidPriceComponent = (components: TariffComponent[]): TariffComponent | null => {
        if (!components || !Array.isArray(components)) return null;

        return components.find(component => {
          const validFrom = new Date(component.validFrom);
          const validTo = new Date(component.validTo);
          return datetimeForPrice >= validFrom && datetimeForPrice <= validTo;
        }) || null;
      };

      // Helper function to find valid block fee component for the given datetime
      const findValidBlockFeeComponent = (components: BlockFeeComponent[]): BlockFeeComponent | null => {
        if (!components || !Array.isArray(components)) return null;

        return components.find(component => {
          const validFrom = new Date(component.validFrom);
          const validTo = new Date(component.validTo);
          return datetimeForPrice >= validFrom && datetimeForPrice <= validTo;
        }) || null;
      };

      // Get the appropriate price components based on charger type
      const priceComponents = chargerType === 'DC' ? tariff.priceDC : tariff.priceAC;
      const blockFeeComponents = chargerType === 'DC' ? tariff.blockFeeDC : tariff.blockFeeAC;

      // Find valid components for the current datetime
      const validPriceComponent = findValidPriceComponent(priceComponents);
      const validBlockFeeComponent = findValidBlockFeeComponent(blockFeeComponents);

      if (!validPriceComponent) {
        console.log(`No valid price component found for ${chargerType} at ${datetimeForPrice.toISOString()}`);
        return defaultPrice;
      }

      // Find the daily schedule for the current day
      const dailySchedule = validPriceComponent.dailySchedules?.find(
        schedule => schedule.dayOfWeek === currentDay
      );

      if (!dailySchedule) {
        console.log(`No daily schedule found for ${currentDay}`);
        return defaultPrice;
      }

      // Find the hourly rate for the current hour
      const hourlyRate = dailySchedule.hourlyRate?.find(
        rate => currentHour >= rate.hourFrom && (rate.hourTo === undefined || currentHour < rate.hourTo)
      );

      if (!hourlyRate) {
        console.log(`No hourly rate found for hour ${currentHour} on ${currentDay}`);
        return defaultPrice;
      }

      // Find the block fee schedule for the current day and hour
      let blockFeeSchedule = null;
      if (validBlockFeeComponent) {
        blockFeeSchedule = validBlockFeeComponent.blockFeeSchedule?.find(
          schedule =>
            schedule.dayOfWeek === currentDay &&
            currentHour >= schedule.startHour &&
            currentHour <= schedule.endHour
        );
      }

      // Handle EPEX vs Fixed pricing
      let pricePerKwh = hourlyRate.pricePerKwh || 0;
      let epexPriceInfo = {};

      if (hourlyRate.priceType === 'EPEX') {
        // Calculate EPEX-based pricing
        const epexResult = await this.calculateEPEXPrice(hourlyRate, datetimeForPrice);
        pricePerKwh = epexResult.pricePerKwh;
        epexPriceInfo = {
          isEPEXPricing: epexResult.isEPEXPricing,
          epexBasePrice: epexResult.epexBasePrice,
          epexCalculatedPrice: epexResult.epexCalculatedPrice,
          fallbackUsed: epexResult.fallbackUsed,
          priceBreakdown: epexResult.priceBreakdown
        };
      }

      // Construct and return the price object
      const result: Price = {
        sessionFee: hourlyRate.sessionFee || 0,
        pricePerKwh,
        perMinute: blockFeeSchedule?.perMinute || 0,
        gracePeriod: blockFeeSchedule?.gracePeriod || 0,
        maxBlockFee: blockFeeSchedule?.maxFee || 0,
        ...epexPriceInfo
      };

      return result;

    } catch (error) {
      console.error('Error in calculatePriceFromTariff:', error);
      return defaultPrice;
    }
  },

  /**
   * Calculate price from stored tariff relation and datetime
   * This method is used when we have a stored tariff relation in the payment session
   */
  async getPriceFromStoredTariff(tariffDocumentId: string, chargerType: 'AC' | 'DC', datetimeForPrice: Date): Promise<Price> {
    const defaultPrice: Price = {
      sessionFee: 0,
      pricePerKwh: 0,
      perMinute: 0,
      gracePeriod: 0,
      maxBlockFee: 0
    };

    try {
      // Get the tariff by documentId with full population
      const tariff = await strapi.documents('api::tariff.tariff').findOne({
        documentId: tariffDocumentId,
        populate: {
          priceAC: {
            populate: {
              dailySchedules: {
                populate: {
                  hourlyRate: true
                }
              }
            }
          },
          priceDC: {
            populate: {
              dailySchedules: {
                populate: {
                  hourlyRate: true
                }
              }
            }
          },
          blockFeeAC: {
            populate: {
              blockFeeSchedule: true
            }
          },
          blockFeeDC: {
            populate: {
              blockFeeSchedule: true
            }
          }
        }
      });

      if (!tariff) {
        console.log(`Stored tariff not found for documentId: ${tariffDocumentId}`);
        return defaultPrice;
      }

      // Calculate the price using the stored tariff
      return await this.calculatePriceFromTariff(tariff, chargerType, datetimeForPrice);

    } catch (error) {
      console.error('Error in getPriceFromStoredTariff:', error);
      return defaultPrice;
    }
  },

  /**
   * Calculate EPEX-based pricing for a given hourly rate and datetime
   */
  async calculateEPEXPrice(hourlyRate: any, datetimeForPrice: Date): Promise<{
    pricePerKwh: number;
    isEPEXPricing: boolean;
    epexBasePrice?: number;
    epexCalculatedPrice?: number;
    fallbackUsed?: boolean;
    priceBreakdown?: {
      epexBase?: number;
      taxesAndLevies?: number;
      margin?: number;
      finalPrice?: number;
    };
  }> {
    try {
      // Get EPEX price for the specific datetime
      const epexPrice = await this.getEPEXPriceForDatetime(datetimeForPrice, hourlyRate.epexAveragingPeriodHours || 1);

      if (!epexPrice) {
        // Use fallback price if EPEX data is not available
        return {
          pricePerKwh: hourlyRate.fallbackPricePerKwh || hourlyRate.pricePerKwh,
          isEPEXPricing: true,
          fallbackUsed: true
        };
      }

      // Convert EPEX price from EUR/MWh to EUR/kWh
      const epexBasePrice = epexPrice / 1000;

      // Add taxes and levies (convert from cents to euros)
      const taxesAndLevies = (hourlyRate.taxesAndLevies || 0) / 100;
      let calculatedPrice = epexBasePrice + taxesAndLevies;

      // Apply margin
      let margin = 0;
      if (hourlyRate.marginType === 'percentage' && hourlyRate.marginValue) {
        margin = calculatedPrice * (hourlyRate.marginValue / 100);
      } else if (hourlyRate.marginType === 'fixed' && hourlyRate.marginValue) {
        margin = hourlyRate.marginValue;
      }
      calculatedPrice += margin;

      // Apply min/max price limits (convert from cents to euros)
      const minPrice = hourlyRate.minPricePerKwh ? hourlyRate.minPricePerKwh / 100 : null;
      const maxPrice = hourlyRate.maxPricePerKwh ? hourlyRate.maxPricePerKwh / 100 : null;

      if (minPrice && calculatedPrice < minPrice) {
        calculatedPrice = minPrice;
      }
      if (maxPrice && calculatedPrice > maxPrice) {
        calculatedPrice = maxPrice;
      }

      return {
        pricePerKwh: calculatedPrice,
        isEPEXPricing: true,
        epexBasePrice,
        epexCalculatedPrice: calculatedPrice,
        fallbackUsed: false,
        priceBreakdown: {
          epexBase: epexBasePrice,
          taxesAndLevies,
          margin,
          finalPrice: calculatedPrice
        }
      };

    } catch (error) {
      console.error('Error calculating EPEX price:', error);
      // Fallback to configured fallback price or fixed price
      return {
        pricePerKwh: hourlyRate.fallbackPricePerKwh || hourlyRate.pricePerKwh,
        isEPEXPricing: true,
        fallbackUsed: true
      };
    }
  },

  /**
   * Get EPEX price for a specific datetime with optional averaging
   */
  async getEPEXPriceForDatetime(datetime: Date, averagingPeriodHours: number = 1): Promise<number | null> {
    try {
      if (averagingPeriodHours === 1) {
        // Get price for the specific hour
        const epexData = await strapi.entityService.findMany('api::epex.epex', {
          filters: {
            timestamp: {
              $gte: new Date(datetime.getFullYear(), datetime.getMonth(), datetime.getDate(), datetime.getHours()).toISOString(),
              $lt: new Date(datetime.getFullYear(), datetime.getMonth(), datetime.getDate(), datetime.getHours() + 1).toISOString()
            }
          },
          limit: 1
        });

        return epexData.length > 0 ? epexData[0].marketprice : null;
      } else {
        // Calculate average price over the specified period
        const startTime = new Date(datetime);
        startTime.setHours(startTime.getHours() - Math.floor(averagingPeriodHours / 2));
        const endTime = new Date(datetime);
        endTime.setHours(endTime.getHours() + Math.ceil(averagingPeriodHours / 2));

        const epexData = await strapi.entityService.findMany('api::epex.epex', {
          filters: {
            timestamp: {
              $gte: startTime.toISOString(),
              $lt: endTime.toISOString()
            }
          }
        });

        if (epexData.length === 0) return null;

        const averagePrice = epexData.reduce((sum, price) => sum + price.marketprice, 0) / epexData.length;
        return averagePrice;
      }
    } catch (error) {
      console.error('Error fetching EPEX price:', error);
      return null;
    }
  },

  async getTariff(evseDocumentId: string, datetimeForPrice: Date): Promise<Price> {
    // Use the new getTariffWithDetails method and return only the price
    const result = await this.getTariffWithDetails(evseDocumentId, datetimeForPrice);
    return result.price;
  }
}));
